package logger

import (
	"context"
	"log/slog"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/go-chi/chi/v5/middleware"
	"github.com/lmittmann/tint"
)

type contextKey struct {
	name string
}

var (
	loggerContextKey = contextKey{"logger"}    // app logger is passed via contaxt - todo we should be able to delete this once we convert to the logattr approach?
	logAttrsKey      = contextKey{"log_attrs"} // log attributes stored in context are logged by the middleware below
)

func ContextWithLogger(ctx context.Context, logger *slog.Logger) context.Context {
	return context.WithValue(ctx, loggerContextKey, logger)
}

func ContextLogger(ctx context.Context) *slog.Logger {
	logger, ok := ctx.Value(loggerContextKey).(*slog.Logger)
	if !ok {
		// fallback
		return slog.Default()
	}
	return logger
}

// ContextWithLogAttr appends slog Attributes to the shares slice held in context (visible to RequestLogger middleware)
func ContextWithLogAttrs(ctx context.Context, attrs ...slog.Attr) context.Context {
	if attrPtr, ok := ctx.Value(logAttrsKey).(*[]slog.Attr); ok {
		*attrPtr = append(*attrPtr, attrs...)
		return ctx
	}

	// Fallback: create a new context with the attributes (for backwards compatibility)
	existing, _ := ctx.Value(logAttrsKey).([]slog.Attr)
	combined := append(existing, attrs...)
	return context.WithValue(ctx, logAttrsKey, combined)
}

func ContextLogAttrs(ctx context.Context) []slog.Attr {
	// Try to get the shared slice pointer first
	if attrPtr, ok := ctx.Value(logAttrsKey).(*[]slog.Attr); ok {
		return *attrPtr
	}

	// Fallback: try to get the slice directly (for backwards compatibility)
	attrs, ok := ctx.Value(logAttrsKey).([]slog.Attr)
	if !ok {
		return nil
	}
	return attrs
}

// ParseLogLevel converts a string log level to slog.Level
func ParseLogLevel(level string) slog.Level {
	switch strings.ToLower(level) {
	case "debug":
		return slog.LevelDebug
	case "info":
		return slog.LevelInfo
	case "warn", "warning":
		return slog.LevelWarn
	case "error":
		return slog.LevelError
	default:
		return slog.LevelDebug // default to debug
	}
}

// InitLogger creates a logger with the specified log level.
// Uses text format for dev environment otherwise output is JSON
func InitLogger(logLevel slog.Level, environment string) *slog.Logger {
	if environment == "dev" {
		// Use colourized text handler for development
		return slog.New(
			tint.NewHandler(os.Stderr, &tint.Options{
				Level:      logLevel,
				TimeFormat: time.Kitchen,
			}),
		)
	} else {
		// Use JSON handler for production
		return slog.New(
			slog.NewJSONHandler(os.Stdout, &slog.HandlerOptions{
				Level: logLevel,
			}))
	}

}

// RequestLogging is a middleware that logs HTTP requests
func RequestLogging(logger *slog.Logger) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Skip logging for health requests
			if strings.HasPrefix(r.URL.Path, "/health/") {
				next.ServeHTTP(w, r)
				return
			}

			start := time.Now()
			requestID := middleware.GetReqID(r.Context())

			// Determine request component based on path
			var component string
			switch {
			case strings.HasPrefix(r.URL.Path, "/docs"):
				component = "docs"
			case strings.HasPrefix(r.URL.Path, "/swagger.json"):
				component = "docs"
			case strings.HasPrefix(r.URL.Path, "/api/"):
				component = "api"
			case strings.HasPrefix(r.URL.Path, "/oauth/"):
				component = "oauth"
			default:
				component = "ui"
			}

			// Create request-scoped logger with common fields
			reqLogger := logger.With(
				slog.String("type", "HTTP"),
				slog.String("request_id", requestID),
				slog.String("method", r.Method),
				slog.String("path", r.URL.Path),
				slog.String("remote_addr", r.RemoteAddr),
				slog.String("component", component),
			)
			ctx := ContextWithLogger(r.Context(), reqLogger)

			// shared slice for attributes that handlers can modify
			sharedAttrs := &[]slog.Attr{}
			ctx = context.WithValue(ctx, logAttrsKey, sharedAttrs)

			req := r.WithContext(ctx)

			// Wrap response writer to capture status
			ww := middleware.NewWrapResponseWriter(w, r.ProtoMajor)

			// pass on updated writer and request
			next.ServeHTTP(ww, req)

			duration := time.Since(start)

			// Prepare base log attributes
			logAttrs := []slog.Attr{
				slog.Int("status", ww.Status()),
				slog.Duration("duration", duration),
				slog.Int("bytes", ww.BytesWritten()),
			}

			// Add any attributes that were added to the context during request processing
			contextAttrs := ContextLogAttrs(req.Context())
			if len(contextAttrs) > 0 {
				logAttrs = append(logAttrs, contextAttrs...)
			}

			// Log request completion with all attributes
			switch {
			case ww.Status() >= 500:
				reqLogger.LogAttrs(r.Context(), slog.LevelError, "request completed", logAttrs...)
			case ww.Status() >= 400:
				reqLogger.LogAttrs(r.Context(), slog.LevelWarn, "request completed", logAttrs...)
			default:
				reqLogger.LogAttrs(r.Context(), slog.LevelInfo, "request completed", logAttrs...)
			}
		})
	}
}
